Stack trace:
Frame         Function      Args
0007FFFF5810  00021005FE8E (000210285F68, 00021026AB6E, 0007FFFF5810, 0007FFFF4710) msys-2.0.dll+0x1FE8E
0007FFFF5810  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFF5AE8) msys-2.0.dll+0x67F9
0007FFFF5810  000210046832 (000210286019, 0007FFFF56C8, 0007FFFF5810, 000000000000) msys-2.0.dll+0x6832
0007FFFF5810  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFF5810  000210068E24 (0007FFFF5820, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFF5AF0  00021006A225 (0007FFFF5820, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FF884070000 ntdll.dll
7FF882530000 KERNEL32.DLL
7FF881960000 KERNELBASE.dll
7FF882700000 USER32.dll
7FF881C60000 win32u.dll
7FF883E60000 GDI32.dll
7FF881F60000 gdi32full.dll
7FF881CC0000 msvcp_win.dll
7FF881860000 ucrtbase.dll
000210040000 msys-2.0.dll
7FF883530000 advapi32.dll
7FF882BF0000 msvcrt.dll
7FF882600000 sechost.dll
7FF882970000 RPCRT4.dll
7FF881C90000 bcrypt.dll
7FF880EF0000 CRYPTBASE.DLL
7FF881DB0000 bcryptPrimitives.dll
7FF882500000 IMM32.DLL
